<template>
  <Navigater :title="t('hongli.title')" />

  <view class="button_group">
    <view class="button" :class="{ active: pageType === 0 }" @click="changePageType(0)">{{ t('hongli.button1') }}</view>
    <view class="button" :class="{ active: pageType === 1 }" @click="changePageType(1)">{{ t('hongli.button2') }}</view>
  </view>

  <view v-show="pageType === 0" class="column flex">
    <view>{{ t('hongli.column_name') }}</view>
    <view>{{ t('hongli.column_jiage') }}</view>
    <view>{{ t('hongli.column_daima') }}</view>
  </view>
  <scroll-view scroll-y class="content">
    <view class="gupiao_list">
      <view v-show="pageType === 0">
        <NotData v-if="gupiaoList.length === 0"></NotData>

        <view v-for="(item, index) in gupiaoList" :key="index" class="gupiao1 flex" @click="openWindow(item)">
          <view class="tt">{{ item?.product?.name }}</view>
          <view class="red">{{ changeMoney(item.buy_in_money) }}</view>
          <view>{{ item?.product?.shuzidaima }}</view>
        </view>
      </view>

      <view v-show="pageType === 1" class="card2">
        <NotData v-if="shenqingList.length === 0"></NotData>

        <view v-for="(item, index) in shenqingList" :key="index" class="gupiao2">
          <div class="top">
            <div class="title">{{ item?.product?.name }}</div>
            <div class="daima">{{ item?.product?.shuzidaima }}</div>
          </div>

          <div class="row">
            <div class="left">
              <view class="label">{{ t('hongli.column_jiage') }}</view>
              <view class="value red">{{ changeMoney(item.buy_in_money) }}</view>
            </div>
            <div class="right">
              <view class="label">{{ t('jiaoyi.shenqingliang') }}</view>
              <view v-if="item.order_status == '2' || item.order_status == '3' || item.order_status == '4'" class="value red">{{ Math.floor(item.final_num) }}</view>
              <view v-else class="value red">{{ Math.floor(item.buy_in_num) }}</view>
            </div>
          </div>

          <div class="row">
            <div class="left">
              <view class="label">{{ t('hongli.column_chengjiaoe') }}</view>
              <view class="value">{{ changeMoney(Math.floor(item.buy_in_amount)) }}</view>
            </div>
            <div class="right">
              <view class="label">{{ t('hongli.column_zhuangtai') }}</view>
              <view v-if="item.status == '2'" class="value" style="color: #1bb600"> {{ t('hongli.tip1') }} </view>
              <view v-if="item.status == '3'" class="value red">{{ t('hongli.tip2') }} </view>
              <view v-if="item.status == '1'" class="value" style="color: #111">{{ t('hongli.tip3') }}</view>
            </div>
          </div>
        </view>
      </view>
    </view>
  </scroll-view>

  <van-popup v-model:show="gupiaoWindowShow" class="buy_window" v-bind="windowOptions">
    <view class="title">{{ t('hongli.title2') }}</view>
    <view class="row_wrap">
      <view class="row" style="width: 100%">
        <view class="label">{{ t('hongli.column_name') }}：</view>
        <view class="value">{{ windowDetail.name }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('hongli.column_daima') }}：</view>
        <view class="value">{{ windowDetail.shuzidaima }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('hongli.window_shijia') }}：</view>
        <view class="value">{{ changeMoney(windowDetail.price) }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('hongli.window_zhangdie') }}：</view>
        <view class="value">{{ windowDetail.zhangdiebaifenbi }}%</view>
      </view>
      <view class="row">
        <view class="label">{{ t('hongli.column_jiage') }}：</view>
        <view class="value">{{ changeMoney(windowDetail.buy_in_money) }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('hongli.window_heji') }}：</view>
        <view class="value">{{ changeMoney(totalPrice) }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('hongli.window_yue') }}：</view>
        <view class="value">{{ changeMoney(userMoneyInfo.money) }}</view>
      </view>

      <view class="row" style="width: 100%; justify-content: space-between">
        <view class="label">{{ t('ipo.windowLabel2') }}（{{ t('ipo.zang') }}）</view>
        <van-stepper v-model="buyParams.num" integer step="1" />
      </view>
    </view>
    <view class="button" @click="buy">{{ t('gupiaoDetail.submit') }}</view>
    <van-icon class="close" name="cross" color="#333" size="1.2rem" @click="gupiaoWindowShow = false" />
  </van-popup>
</template>

<script setup lang="ts">
import { getHongliListApi, buyHongliApi, getUserMoneyApi } from '@/api/index/index'
import { onHide, onShow } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
import { changeMoney } from '@/common/common'
import { ref, computed } from 'vue'
import { showLoadingToast, showToast } from 'vant'
const { t } = useI18n()
let inId: any = null
onShow(() => {
  getHongliList()
  getUserMoney()
  inId = setInterval(() => {
    getHongliList()
    getUserMoney()
  }, 10000)
})

onHide(() => {
  clearInterval(inId)
})
const pageType = ref(0)
const changePageType = async (e: number) => {
  pageType.value = e
}

// 獲取新股列表
const gupiaoList = ref([])
const shenqingList = ref([])
const getHongliList = async () => {
  const res = await getHongliListApi()
  gupiaoList.value = res.data.pro_list
  shenqingList.value = res.data.order_list
}

// 獲取手續費
const userMoneyInfo = ref({})
const getUserMoney = async () => {
  const res = await getUserMoneyApi()
  res.data.sxf = Number(res.data.sxf)
  res.data.sxfzdi = Number(res.data.sxfzdi)
  userMoneyInfo.value = res.data
}

// 彈窗
const gupiaoWindowShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true,
  round: true
}
const windowDetail = ref({
  hedging_price: 0
})
const openWindow = (e: any) => {
  windowDetail.value = e
  gupiaoWindowShow.value = true
}
const totalPrice = computed(() => {
  return buyParams.value.num * 100 * Number(windowDetail.value.buy_in_money)
})

// 購買
const buyParams = ref({
  dividend_id: 0,
  num: 1
})
const buy = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  buyParams.value.dividend_id = windowDetail.value.id
  const res = await buyHongliApi(buyParams.value)
  gupiaoWindowShow.value = false
  getHongliList()
  getUserMoney()
  if (res.code === 1) {
    showToast({
      message: res.msg
    })
  }
}
</script>

<style scoped lang="scss">
.content {
  height: calc(var(--vh) * 100 - 3.13rem - 2.75rem - 2.5rem);
  overflow: auto;

  .card2 {
    padding: 0.75rem 0;
  }
}
.column {
  padding: 0.52rem 0rem;
  margin: 0 0.63rem;
  display: flex;
  text-align: center;
  height: 2.5rem;
  align-items: center;
  > view {
    flex: 1;
    color: #a0a5b0;
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.a {
  align-items: center;
}

.button_group {
  height: 2.75rem;
  margin: 0 0.94rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.31rem;
  .button {
    width: 50%;
    height: 2.75rem;
    font-size: 0.88rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $color-white;
    border: 0.1rem solid $color-primary;
    color: $color-black;
    border-radius: 0.5rem;
  }
  .active {
    background: $color-primary;
    border: 0.05rem solid $color-primary;
    color: $color-white;
    font-weight: 500;
  }
}
.gupiao_list {
  margin-bottom: 0.31rem;
  align-items: center;
  justify-content: center;
  width: 100%;
  .column,
  .gupiao {
    height: 3.5rem;
    padding: 0.52rem 0rem;
    display: flex;
    background-color: rgb(10, 31, 55, 0.7);
    border-radius: 0.3125rem;
    text-align: center;
    margin-bottom: 0.625rem;
    > view {
      flex: 1;
      font-size: $uni-font-size-lg;
      white-space: pre-wrap;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .gupiao1 {
    height: 2.81rem;
    padding: 0.52rem;
    display: flex;
    border-bottom: 1px solid rgba(20, 20, 20, 0.17);
    text-align: center;
    margin-bottom: 0.1rem;
    margin: 0 0.63rem;
    > view {
      flex: 1;
      font-size: $uni-font-size-lg;
      white-space: pre-wrap;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      color: $color-gray;
      text-overflow: ellipsis;
    }
    .tt {
      color: $color-black;
    }
  }

  .gupiao2 {
    margin: 0 1rem;
    padding: 0.5rem;
    border-bottom: 1px solid rgba(20, 20, 20, 0.17);
    .top {
      display: flex;
      align-items: center;
      gap: 1.56rem;
      .title {
        font-size: 0.94rem;
        font-weight: 500;
        color: $color-black;
      }
      .daima {
        height: 1.19rem;
        border-radius: 0.31rem;
        color: $color-white;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        background: $color-primary;
        padding: 0.2rem 0.5rem;
      }
    }
    .row {
      justify-content: space-between;
      align-items: center;
      .left,
      .right {
        display: flex;
        justify-content: space-between;
        .label {
          color: $color-gray;
          font-size: 0.88rem;
          &::after {
            content: '：';
          }
        }
        .value {
          color: $color-black;
          font-size: 0.88rem;
        }
      }
    }
  }
  .gupiao2 + .gupiao2 {
    margin-top: 0.31rem;
  }
  .text_ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    word-break: break-all;
    width: 100%;
  }
}

.buy_window {
  padding: 0 0.8rem 3rem;
  background: #fff;
  .title {
    text-align: center;
    padding: 0.8rem 0 0.5rem;
    font-size: 1rem;
    color: $color-black;
  }
  .row_wrap {
    display: flex;
    flex-wrap: wrap;

    .row {
      &:nth-of-type(2n + 1) {
        width: 55%;
      }
      display: flex;
      width: 45%;
      align-items: center;
      padding: 0.24rem 0.56rem;
      color: #fff;
      .label {
        color: $color-gray;
      }
      view {
        color: $color-black;
        font-size: 0.8rem;
      }
    }
  }
  .button {
    height: 3.06rem;
    line-height: 3.06rem;
    width: 100%;
    text-align: center;
    border-radius: 1.25rem;
    background-color: $color-primary;
    color: #fff;
    font-size: 0.81rem;
    margin-top: 1rem;
  }
  .close {
    position: absolute;
    width: 2.75rem;
    height: 2.75rem;
    padding: 1rem;
    right: 0.5rem;
    top: 0.5rem;
  }
}

::v-deep .van-stepper__minus,
::v-deep .van-stepper__plus {
  width: 1.63rem;
  height: 1.63rem;
  border: 0.06rem solid $color-primary;
  border-radius: 50%;
  background: transparent;
  &::before {
    background: $color-primary;
  }
  &::after {
    background: $color-primary;
  }
}
::v-deep .van-stepper__minus--disabled,
::v-deep .van-stepper__plus--disabled {
  border-color: #ccc;
  &::before {
    background: #ccc;
  }
  &::after {
    background: #ccc;
  }
}

::v-deep .van-stepper__input {
  background: transparent;
  margin: 0 0.63rem;
  width: 2.6rem;
  color: #333;
}
</style>
