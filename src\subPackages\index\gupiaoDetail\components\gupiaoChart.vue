<template>
  <view id="chart" style="width: 100%; height: 30.31rem"></view>
</template>

<script lang="ts" setup>
import { init, dispose } from 'klinecharts'
import { onMounted, onUnmounted, watch } from 'vue'
const props = defineProps({
  value: {
    type: Array,
    default: () => []
  }
})
let chart: any = null
watch(
  () => props.value,
  (newData) => {
    chart?.applyNewData(newData)
  },
  { deep: true }
)
const chartOption = {
  grid: {
    show: true,
    horizontal: {
      show: false,
      size: 1,
      color: '#333',
      style: 'dashed',
      dashedValue: [2, 2]
    },
    vertical: {
      show: false,
      size: 1,
      color: '#333',
      style: 'dashed',
      dashedValue: [2, 2]
    }
  },
  // 蜡烛图
  candle: {
    type: 'candle_solid',
    // 网格线
    // 提示
    tooltip: {
      showRule: 'none'
    },
    bar: {
      upColor: '#1bb600',
      downColor: '#ff6254',
      noChangeColor: '#888888',
      upBorderColor: '#1bb600',
      downBorderColor: '#ff6254',
      noChangeBorderColor: '#888888',
      upWickColor: '#1bb600',
      downWickColor: '#ff6254',
      noChangeWickColor: '#888888'
    }
  },
  indicator: {
    bars: [
      {
        upColor: '#1bb600',
        downColor: '#ff6254'
      }
    ]
  },
  // x轴
  xAxis: {
    // x轴线
    axisLine: {
      show: true,
      color: '#e5e8f6'
    }
  },
  // y轴
  yAxis: {
    // y轴线
    axisLine: {
      color: '#e5e8f6'
    },
    // x轴分割文字
    tickText: {
      color: '#666'
    },
    // x轴分割线
    tickLine: {
      color: '#e5e8f6'
    }
  },
  // 图表之间的分割线
  separator: {
    color: '#e5e8f6'
  }
}
onMounted(() => {
  chart = init('chart')
  chart.setStyles(chartOption)
  chart.createIndicator('MA', false, { id: 'candle_pane' })
  chart.createIndicator('VOL')
  chart.createIndicator('KDJ')
})

onUnmounted(() => {
  dispose('chart')
})
</script>
<style scoped lang="scss"></style>
