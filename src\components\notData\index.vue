<template>
  <view class="nodataWrap">
    <view class="box">
      <img :src="nodata" mode="" />
      <view v-if="!props.title" class="title">{{ t('common.nodata') }}</view>
      <view v-else class="title">{{ props.title }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import nodata from '@/static/image/components/nodata.png'
const { t } = useI18n()
const props = defineProps({
  title: {
    type: Number,
    default: 0
  }
})
</script>

<style lang="scss" scoped>
.nodataWrap {
  overflow: hidden;
}
.box {
  margin: 5rem auto 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: 16.88rem;
    margin-bottom: -2rem;
  }
  .title {
    color: #a6a6a6;
    font-size: 0.84rem;
    margin-top: 0.5rem;
  }
}
</style>
