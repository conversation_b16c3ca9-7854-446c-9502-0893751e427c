// 前往下一个页面
export function goPage(e: string) {
  uni.navigateTo({
    url: e
  })
}

// 返回下一个页面
export function goBack(delta: number = 1) {
  if (window.history.length > 1) {
    window.history.back()
  } else {
    uni.navigateBack({})
  }
}

// 删除当前页并前往下一个页面
export function reLaunch(e: string) {
  uni.reLaunch({
    url: e
  })
}

// 跳转到tabbar
export function switchTab(e: string) {
  uni.switchTab({
    url: e
  })
}

// 时间戳转换
export const timestempToDate = (timestemp: number, n: string = '-') => {
  const date = new Date(timestemp)
  const year = date.getFullYear()
  const month = date.getMonth() - 1 < 10 ? `0${date.getMonth() - 1}` : date.getMonth() - 1
  const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()
  const hour = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()
  const minute = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()
  return `${year}${n}${month}${n}${day} ${hour}:${minute}`
}

// function convertBeijingToTokyo(beijingDate) {
//   const tokyoTime = new Date(beijingDate.getTime() + 1 * 60 * 60 * 1000)

//   return tokyoTime
// }

// export function timestampToDate3(date) {
//   convertBeijingToTokyo(new Data)
// }

export function timestampToDate2(timestamp: number) {
  const date = new Date(timestamp) // 时间戳转换成Date对象
  // console.log(convertBeijingToTokyo(date), 'convertBeijingToTokyo(date)')
  // date = convertBeijingToTokyo(date)
  const year = date.getFullYear() // 获取年份
  let month = date.getMonth() + 1 // 获取月份，需要+1因为月份从0开始
  let day = date.getDate() // 获取日
  let hours = date.getHours() // 获取小时
  let minutes = date.getMinutes() // 获取分钟
  let seconds = date.getSeconds() // 获取秒钟

  // 格式化月份、日期、小时、分钟和秒
  month = month < 10 ? '0' + month : month
  day = day < 10 ? '0' + day : day
  hours = hours < 10 ? '0' + hours : hours
  minutes = minutes < 10 ? '0' + minutes : minutes
  seconds = seconds < 10 ? '0' + seconds : seconds

  // 组合成日期时间字符串
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}
// 获取指定时区的时间戳；因为目前还无法直接修改date对象的时区，所以使用时间戳方式返回
function getZoneTime(date, zone) {
  const offset_GMT = date.getTimezoneOffset()
  const current = date.getTime()
  const targetDate = new Date(current + offset_GMT * 60 * 1000 + zone * 60 * 60 * 1000)
  return targetDate.getTime()
}

export function timestampToDate3(timestamp: number) {
  let date = new Date(timestamp) // 时间戳转换成Date对象
  // console.log(convertBeijingToTokyo(date), 'convertBeijingToTokyo(date)')
  date = new Date(getZoneTime(date, 9))
  const year = date.getFullYear() // 获取年份
  let month = date.getMonth() + 1 // 获取月份，需要+1因为月份从0开始
  let day = date.getDate() // 获取日
  let hours = date.getHours() // 获取小时
  let minutes = date.getMinutes() // 获取分钟
  let seconds = date.getSeconds() // 获取秒钟

  // 格式化月份、日期、小时、分钟和秒
  month = month < 10 ? '0' + month : month
  day = day < 10 ? '0' + day : day
  hours = hours < 10 ? '0' + hours : hours
  minutes = minutes < 10 ? '0' + minutes : minutes
  seconds = seconds < 10 ? '0' + seconds : seconds

  // 组合成日期时间字符串
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

// 获取股票颜色
export const getColor = (item: any, type: boolean = false) => {
  if (type && item.zhangdiebaifenbi >= 0) {
    return 'green rotate'
  }
  return item.zhangdiebaifenbi >= 0 ? 'green' : 'red'
}

// 获取股票颜色
export const getColor2 = (item: any) => {
  return Number(item) >= 0 ? 'green' : 'red'
}

// 验证输入框内容是否为空
interface checkType {
  key: string
  message: string
}
interface dataType {
  [key: string]: string | null
}
export function checkInput(checkArr: Array<checkType>, data: dataType) {
  for (const i in checkArr) {
    if (!data[checkArr[i].key]) {
      uni.showToast({
        title: checkArr[i].message,
        icon: 'none'
      })
      return false
    }
  }
  return true
}

// 价格转换（加逗号）
export const changeMoney = (money: any) => {
  money = Number(money).toFixed(1)
  money += ''
  const x = money.split('.')
  let x1 = x[0]
  const x2 = x.length > 1 ? '.' + x[1] : ''
  const reg = /(\d+)(\d{3})/
  while (reg.test(x1)) {
    x1 = x1.replace(reg, '$1' + ',' + '$2')
  }
  return x1 + x2
}

// 全角转半角
export const toHalfWidth = (str: any) => {
  return (
    str
      .replace(/[！-～]/g, function (char) {
        return String.fromCharCode(char.charCodeAt(0) - 0xfee0)
      })
      .replace(/[ａ-ｚＡ-Ｚ０-９]/g, function (char) {
        return String.fromCharCode(char.charCodeAt(0) - 0xfee0)
      })
      // eslint-disable-next-line no-irregular-whitespace
      .replace(/　/g, ' ')
  ) // 处理全角空格
}

// 全角转半角 整数
export const toHalfWidthnumber = (str: number) => {
  return Number(
    str.toString().replace(/[\uff10-\uff19]/g, function (c) {
      return String.fromCharCode(c.charCodeAt(0) - 0xfee0)
    })
  )
}
