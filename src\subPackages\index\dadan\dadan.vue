<template>
  <Navigater :title="t('dadan.title')" />

  <view class="gupiao_list">
    <view class="button_group">
      <view class="button" :class="{ active: pageType === 0 }" @click="changePageType(0)">{{ t('hongli.button1') }}</view>
      <view class="button" :class="{ active: pageType === 1 }" @click="changePageType(1)">{{ t('hongli.button2') }}</view>
    </view>
    <view v-show="pageType === 0" class="column flex">
      <view class="text-center">{{ t('dadan.column_name') }}</view>
      <view class="text-center">{{ t('dadan.column_daima') }}</view>
      <view class="text-center">{{ t('dadan.column_xianjia') }}</view>
      <view class="text-center">{{ t('dadan.column_jiage') }}</view>
    </view>
    <scroll-view scroll-y class="content">
      <view v-for="(item, index) in dadanList" v-show="pageType === 0" :key="index" class="gupiao1 flex" @click="openWindow(item)">
        <view class="tt">{{ item.product_name }}</view>
        <view class="text-center">{{ item.product_code }}</view>
        <view class="red">{{ changeMoney(item.now_price) }}</view>
        <view class="red">{{ changeMoney(item.hedging_price) }}</view>
      </view>
      <view v-show="pageType === 1" class="card2">
        <view v-for="(item, index) in shenqingList" :key="index" class="gupiao2">
          <div class="top">
            <div class="title">{{ item?.product_name }}</div>
            <div class="daima">{{ item?.product_code }}</div>
          </div>

          <div class="row">
            <div class="left">
              <view class="label">{{ t('hongli.column_jiage') }}</view>
              <view class="value red">{{ changeMoney(item.buy_in_price) }}</view>
            </div>
            <div class="right">
              <view class="label">{{ t('jiaoyi.shenqingliang') }}</view>
              <view class="value red">{{ Math.floor(item.buy_in_num * 100) }}</view>
            </div>
          </div>

          <div class="row">
            <div class="left">
              <view class="label">{{ t('hongli.column_chengjiaoe') }}</view>
              <view class="value red">{{ changeMoney(Math.floor(item.buy_in_amount)) }}</view>
            </div>
            <div class="right">
              <view class="label">{{ t('hongli.column_zhuangtai') }}</view>
              <view v-if="item.status == '2'" class="value" style="color: #1bb600"> {{ t('hongli.tip1') }} </view>
              <view v-if="item.status == '3'" class="value red">{{ t('hongli.tip2') }} </view>
              <view v-if="item.status == '1'" class="value" style="color: #111">{{ t('hongli.tip3') }}</view>
            </div>
          </div>
        </view>
      </view>
      <NotData v-if="(pageType === 0 && dadanList.length === 0) || (pageType === 1 && shenqingList.length === 0)" />
    </scroll-view>
  </view>

  <van-popup v-model:show="gupiaoWindowShow" class="buy_window" v-bind="windowOptions">
    <view class="title">{{ t('dadan.title2') }}</view>
    <view class="row_wrap">
      <view class="row" style="width: 100%">
        <view class="label">{{ t('dadan.column_name') }}：</view>
        <view class="value text_ellipsis">{{ windowDetail.product_name }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('dadan.column_daima') }}：</view>
        <view class="value">{{ windowDetail.product_code }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('dadan.column_jiage') }}：</view>
        <view class="value">{{ windowDetail.hedging_price }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('dadan.heji') }}：</view>
        <view class="value">{{ changeMoney(totalPrice) }}</view>
      </view>
      <view class="row">
        <view class="label" style="color: red">{{ t('dadan.zhuyi') }}：</view>
        <view class="value" style="color: red">{{ `1${t('dadan.unit')}=100${t('dadan.unit2')}` }}</view>
      </view>
    </view>
    <view class="flex justify-center my-[.8rem]">
      <van-stepper v-model="buyParams.num" input-width="100" button-size="2rem" integer />
    </view>

    <view class="button" @click="buy">{{ t('gupiaoDetail.submit') }}</view>
    <van-icon class="close" name="cross" color="#333" size="1.2rem" @click="gupiaoWindowShow = false" />
  </van-popup>
</template>

<script setup lang="ts">
import { getDadanListApi, buyDadanApi, getUserMoneyApi, getUserMonitoringApi } from '@/api/index/index'
import { onHide, onLoad, onShow } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
import { ref, computed } from 'vue'
import { showLoadingToast, showToast } from 'vant'
import { changeMoney } from '@/common/common'
const { t } = useI18n()

onLoad(() => {
  getUserMoney()
})
let id: any = null
onShow(() => {
  getUserMonitoringFn()
  getXinguList()
  id = setInterval(() => {
    getXinguList()
  }, 20000)
})
onHide(() => {
  clearInterval(id)
})

const pageType = ref(0)
const changePageType = async (e: number) => {
  pageType.value = e
}

// 获取申请列表数据
const shenqingList = ref([])
const getUserMonitoringFn = async () => {
  const res = await getUserMonitoringApi()
  if (res.code === 1) {
    shenqingList.value = res.data.data
  }
  console.log(shenqingList.value, 'shenqingList.value')
}

// 獲取新股列表
const dadanList = ref([])
const getXinguList = async () => {
  const res = await getDadanListApi()
  dadanList.value = res.data
}

// 獲取手續費
const userMoneyInfo = ref({})
const getUserMoney = async () => {
  const res = await getUserMoneyApi()
  res.data.sxf = Number(res.data.sxf)
  res.data.sxfzdi = Number(res.data.sxfzdi)
  userMoneyInfo.value = res.data
}

// 彈窗
const gupiaoWindowShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true,
  round: true
}
const windowDetail = ref({
  hedging_price: 0
})
const openWindow = (e: any) => {
  windowDetail.value = e
  gupiaoWindowShow.value = true
}
const totalPrice = computed(() => {
  return buyParams.value.num * 100 * Number(windowDetail.value.hedging_price)
})

// 購買
const buyParams = ref({
  monitoring_id: '',
  num: 1
})
const buy = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  buyParams.value.monitoring_id = windowDetail.value.id
  const res = await buyDadanApi(buyParams.value)
  gupiaoWindowShow.value = false
  if (res.code === 1) {
    getUserMonitoringFn()
    showToast(res.msg)
  }
}
</script>

<style scoped lang="scss">
.content {
  height: calc(var(--vh) * 100 - 3.13rem - 2.75rem - 2.5rem);
  overflow: auto;

  .card2 {
    padding: 0.75rem 0;
  }
}
uni-view {
  text-align: center;
}
.flex_direction {
  flex-direction: column;
}
.text-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex1 {
  width: 25%;
}
.text_ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.gupiao_list {
  margin-bottom: 0.31rem;
  .button_group {
    height: 2.75rem;
    margin: 0 0.94rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.31rem;
    .button {
      width: 50%;
      height: 2.75rem;
      font-size: 0.88rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $color-white;
      border: 0.1rem solid $color-primary;
      color: $color-black;
      border-radius: 0.5rem;
    }
    .active {
      background: $color-primary;
      border: 0.05rem solid $color-primary;
      color: $color-white;
      font-weight: 500;
    }
  }
  .column,
  .gupiao {
    padding: 0.52rem 0rem;
    margin: 0 0.94rem;
    display: flex;
    text-align: center;
    height: 2.5rem;
    align-items: center;
    > view {
      flex: 1;
      color: #a0a5b0;
      font-size: 0.75rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .gupiao1 {
    height: 2.81rem;
    padding: 0.52rem;
    display: flex;
    border-bottom: 1px solid rgba(20, 20, 20, 0.17);
    text-align: center;
    align-items: center;
    margin: 0 0.63rem;
    margin-bottom: 0.1rem;
    > view {
      flex: 1;
      font-size: $uni-font-size-lg;
      white-space: pre-wrap;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      color: $color-gray;
      text-overflow: ellipsis;
    }
    .tt {
      color: $color-black;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
  }
  .gupiao2 {
    margin: 0 1rem;
    padding: 0.5rem;
    border-bottom: 1px solid rgba(20, 20, 20, 0.17);
    .top {
      display: flex;
      align-items: center;
      gap: 1.56rem;
      .title {
        font-size: 0.94rem;
        font-weight: 500;
        color: $color-black;
        text-align: left;
      }
      .daima {
        height: 1.19rem;
        border-radius: 0.31rem;
        color: $color-white;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        background: $color-primary;
        padding: 0.2rem 0.5rem;
      }
    }
    .row {
      justify-content: space-between;
      align-items: center;
      .left,
      .right {
        display: flex;
        justify-content: space-between;
        .label {
          color: $color-gray;
          font-size: 0.88rem;
          &::after {
            content: '：';
          }
        }
        .value {
          color: $color-gray;
          font-size: 0.88rem;
        }
      }
    }
  }
  .gupiao2 + .gupiao2 {
    margin-top: 0.31rem;
  }
  .column {
    text-align: center;
  }
}

.buy_window {
  padding: 0 0.8rem 3rem;
  background: #fff;
  .title {
    text-align: center;
    padding: 0.8rem 0 0.5rem;
    font-size: 1rem;
    color: $color-black;
  }
  .row_wrap {
    display: flex;
    flex-wrap: wrap;
    .row {
      display: flex;
      align-items: center;
      width: 50%;
      padding: 0.24rem 0.56rem;
      color: #fff;
      .label {
        color: $color-gray;
      }
      view {
        color: $color-black;
        font-size: 0.775rem;
      }
    }
  }
  .button {
    height: 3.06rem;
    line-height: 3.06rem;
    width: 100%;
    text-align: center;
    border-radius: 0.5rem;
    background-color: $color-primary;
    color: #fff;
    font-size: 0.81rem;
    margin-top: 1rem;
  }
  .close {
    position: absolute;
    width: 2.75rem;
    height: 2.75rem;
    padding: 1rem;
    right: 0.5rem;
    top: 0.5rem;
  }
}
::v-deep .van-stepper__minus,
::v-deep .van-stepper__plus {
  width: 1.63rem;
  height: 1.63rem;
  border: 0.06rem solid $color-primary;
  border-radius: 50%;
  background: transparent;
  &::before {
    background: $color-primary;
  }
  &::after {
    background: $color-primary;
  }
}
::v-deep .van-stepper__minus--disabled,
::v-deep .van-stepper__plus--disabled {
  border-color: #ccc;
  &::before {
    background: #ccc;
  }
  &::after {
    background: #ccc;
  }
}

::v-deep .van-stepper__input {
  background: transparent;
  margin: 0 0.63rem;
  width: 2.6rem;
  color: #333;
}
</style>
