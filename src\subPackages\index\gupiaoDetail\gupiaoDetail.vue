<template>
  <Navigater v-if="isShow" :isdma="`(${gupiaoDetail.shuzidaima})`" :title="`${gupiaoDetail.name}`">
    <template #right>
      <div class="collect" @click="collect">
        <image v-if="collectInfo == 0" src="/static/image/index/star-o.png" mode="" />
        <image v-else src="/static/image/index/star.png" mode="" />
      </div>
    </template>
  </Navigater>
  <div v-if="isShow" class="content" :style="{ height: contentHeight }">
    <div class="head">
      <div class="left">
        <div class="title">{{ gupiaoDetail.name }}</div>
        <div class="daima">{{ gupiaoDetail.shuzidaima }}</div>
      </div>
      <div class="right">
        <div class="price">{{ gupiaoDetail.price }}</div>
      </div>
    </div>
    <div class="zd">
      <div class="text-primary">
        <van-icon :class="getColor(gupiaoDetail, true)" name="down" size=".9rem" />
      </div>
      <div class="" :class="getColor(gupiaoDetail)">({{ gupiaoDetail.zhangdiebaifenbi }}%)</div>
    </div>
    <div class="card">
      <div class="flex justify-between">
        <div class="flex flex-1 text-left de" style="width: 9.375rem">
          <div class="text-gray">{{ t('gupiaoDetail.label1') }}：</div>
          <div class="text-value">{{ gupiaoDetail.open }}</div>
        </div>
        <div class="flex flex-1 text-left de2" style="width: 9.375rem">
          <div class="text-gray">{{ t('gupiaoDetail.label2') }}：</div>
          <div class="text-value">{{ gupiaoDetail.close }}</div>
        </div>
      </div>
      <div class="flex justify-between">
        <div class="flex flex-1 text-left de" style="width: 9.375rem">
          <div class="text-gray">{{ t('gupiaoDetail.label3') }}：</div>
          <div class="text-value" style="color: #ff0021">{{ gupiaoDetail.high }}</div>
        </div>
        <div class="flex flex-1 text-left de2" style="width: 9.375rem">
          <div class="text-gray">{{ t('gupiaoDetail.label4') }}：</div>
          <div class="text-value">{{ gupiaoDetail.low }}</div>
        </div>
      </div>
      <div class="flex justify-between">
        <div class="flex flex-1 text-left de" style="width: 9.375rem">
          <div class="text-gray">{{ t('gupiaoDetail.label5') }}：</div>
          <div class="text-value" style="color: #ff0021">{{ gupiaoDetail.vol }}</div>
        </div>
        <div class="flex flex-1 text-left de2" style="width: 9.375rem">
          <div class="text-gray">{{ t('gupiaoDetail.label6') }}：</div>
          <div class="text-value">{{ gupiaoDetail.totalMoney }}({{ t('gupiaoDetail.yi') }})</div>
        </div>
      </div>
    </div>

    <div class="date_wrap">
      <div v-for="(item, index) in columns" :key="index" :class="{ active: item.value == dateActive }" class="date">
        <span @click="getChart(gupiaoDetail.id, item.value)">{{ item.label }}</span>
        <div v-if="index < columns.length - 1" class="line"></div>
      </div>
    </div>
    <div class="chart_wrap">
      <GupiaoChart :value="chartValue" />
    </div>

    <div class="box">
      <div class="button_wrap">
        <div class="button" :class="{ active: buyGupiaoParams.buytype === 0 }" @click="changeType(0)">{{ t('gupiaoDetail.shijia') }}</div>
        <div class="button" :class="{ active: buyGupiaoParams.buytype === 1 }" @click="changeType(1)">{{ t('gupiaoDetail.xianjia') }}</div>
      </div>
      <div class="submit_wrap">
        <div v-show="buyGupiaoParams.buytype === 1" class="row">
          <div class="title">{{ t('gupiaoDetail.inputLabel1') }}</div>
          <div class="wrap">
            <van-stepper v-model="price" :placeholder="buyGupiaoParams.price" input-width="50" min="0" button-size="1.8rem" step="0.1" @blur="priceChange" />
          </div>
        </div>
        <div class="row">
          <div class="title">{{ t('gupiaoDetail.inputLabel2') }}</div>
          <div class="wrap">
            <van-stepper v-model="buyGupiaoParams.shuliang" input-width="50" button-size="1.8rem" digit />
          </div>
        </div>
        <div v-if="userMoneyInfo.peizi_data === '1'" class="row">
          <div class="title">{{ t('gupiaoDetail.inputLabel3') }}</div>
          <div class="wrap">
            <van-stepper v-model="buyGupiaoParams.ganggan_ratio" input-width="50" button-size="1.8rem" digit @update:model-value="changeganggan_ratio" />
          </div>
        </div>
        <div class="button_wrap">
          <div class="button" :class="{ active: buyGupiaoParams.fangxiang === 'duo' }" @click="buyGupiaoParams.fangxiang = 'duo'">{{ t('gupiaoDetail.maiduo') }}</div>
          <div class="button" :class="{ active: buyGupiaoParams.fangxiang === 'kong' }" @click="buyGupiaoParams.fangxiang = 'kong'">{{ t('gupiaoDetail.maikong') }}</div>
        </div>
        <div class="button submit" @click="openWindow()">{{ t('gupiaoDetail.submit') }}</div>
      </div>
    </div>
  </div>
  <van-popup v-model:show="gupiaoWindowShow" class="buy_window" v-bind="windowOptions">
    <van-icon class="close" name="cross" color="#333" size="1.2rem" @click="gupiaoWindowShow = false" />
    <div class="title row">{{ gupiaoDetail.name }}/{{ gupiaoDetail.shuzidaima }}</div>
    <div class="row_group">
      <div class="row">
        <div class="label">{{ t('gupiaoDetail.windowLabel1') }}</div>
        <div v-if="buyGupiaoParams.buytype == 0" class="value">{{ gupiaoDetail.price ? changeMoney(gupiaoDetail.price) : '-' }}</div>
        <div v-else class="value">{{ price ? changeMoney(price) : gupiaoDetail.price ? changeMoney(gupiaoDetail.price) : '-' }}</div>
      </div>
      <div class="row">
        <div class="label">{{ t('gupiaoDetail.windowLabel2') }}</div>
        <div class="value">{{ buyGupiaoParams.shuliang }}</div>
      </div>
      <div class="row">
        <div class="label">{{ t('gupiaoDetail.windowLabel3') }}</div>
        <div v-if="buyGupiaoParams.buytype == 0" class="value">
          {{
            gupiaoDetail.price !== '-'
              ? userMoneyInfo.peizi_data === '1'
                ? changeMoney((gupiaoDetail.price * buyGupiaoParams.shuliang) / buyGupiaoParams.ganggan_ratio)
                : changeMoney(gupiaoDetail.price * buyGupiaoParams.shuliang)
              : '-'
          }}
        </div>
        <div v-else class="value">
          {{
            price !== ''
              ? buyGupiaoParams.price
                ? userMoneyInfo.peizi_data === '1'
                  ? changeMoney((price * buyGupiaoParams.shuliang) / buyGupiaoParams.ganggan_ratio)
                  : changeMoney(price * buyGupiaoParams.shuliang)
                : '-'
              : gupiaoDetail.price !== '-'
              ? userMoneyInfo.peizi_data === '1'
                ? changeMoney((gupiaoDetail.price * buyGupiaoParams.shuliang) / buyGupiaoParams.ganggan_ratio)
                : changeMoney(gupiaoDetail.price * buyGupiaoParams.shuliang)
              : '-'
          }}
        </div>
      </div>
      <div class="row">
        <div class="label">{{ t('gupiaoDetail.windowLabel4') }}</div>
        <div class="value">{{ gupiaoDetail.price !== '-' ? changeMoney(gupiaoDetail.price * buyGupiaoParams.shuliang) : '-' }}</div>
      </div>
      <div class="row">
        <div class="label">{{ t('gupiaoDetail.windowLabel6') }}</div>
        <div v-if="buyGupiaoParams.buytype === 0" class="value">{{ userMoneyInfo.peizi_data === '1' ? changeMoney(heji / buyGupiaoParams.ganggan_ratio) : changeMoney(heji) }}</div>
        <div v-else class="value">
          {{
            price === ''
              ? userMoneyInfo.peizi_data === '1'
                ? changeMoney((gupiaoDetail.price * (buyGupiaoParams.shuliang || 0)) / buyGupiaoParams.ganggan_ratio)
                : changeMoney(gupiaoDetail.price * (buyGupiaoParams.shuliang || 0))
              : userMoneyInfo.peizi_data === '1'
              ? changeMoney((price * (buyGupiaoParams.shuliang || 0)) / buyGupiaoParams.ganggan_ratio)
              : changeMoney(price * (buyGupiaoParams.shuliang || 0))
          }}
        </div>
      </div>
      <div class="row">
        <div class="label">{{ t('gupiaoDetail.windowLabel7') }}</div>
        <div class="value">{{ changeMoney(userMoneyInfo.money) }}</div>
      </div>
    </div>
    <div class="button" @click="buyGupiao">{{ t('gupiaoDetail.submit') }}</div>
  </van-popup>
</template>
<script setup lang="ts">
import GupiaoChart from './components/gupiaoChart.vue'
import { useCounterStore } from '@/store/store'
import { useI18n } from 'vue-i18n'
import { getGupiaoDetailApi, getTickerKApi, collectApi, collectDetailApi, getUserMoneyApi, buyGupiaoApi } from '@/api/index/index'
import { timestempToDate, getColor, changeMoney } from '@/common/common'
import { onHide, onLoad, onShow } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { buyGupiaoType } from '@/api/index/indexType'
import { closeToast, showConfirmDialog, showLoadingToast, showToast } from 'vant'
const { t } = useI18n()
// 页面基础配置
const store = useCounterStore()
console.log(store)
const contentHeight = `calc(${store.pageHeight} - 3.125rem)`
onLoad(async (options) => {
  getUserMoney()
  buyGupiaoParams.value.pro_id = options?.id
})

const changeganggan_ratio = () => {
  if (buyGupiaoParams.value.ganggan_ratio < 1) {
    buyGupiaoParams.value.ganggan_ratio = 1
  } else if (buyGupiaoParams.value.ganggan_ratio > 100) {
    buyGupiaoParams.value.ganggan_ratio = 100
  }
}

let gpdId: any = null

onShow(async () => {
  await getGupiaoDetail(buyGupiaoParams.value.pro_id)
  getChart(gupiaoDetail.value.id, dateActive.value)
  gpdId = setInterval(async () => {
    await getGupiaoDetail(buyGupiaoParams.value.pro_id)

    await getChart(gupiaoDetail.value.id, dateActive.value)
  }, 10000)
})
onHide(() => {
  clearInterval(gpdId)
})

// 获取股票详情

const gupiaoDetail = ref({
  is_zixuan: 0,
  price: '-'
})
const price = ref('')
const isShow = ref(false)
const getGupiaoDetail = async (id: any) => {
  const res = await getGupiaoDetailApi({ id })
  isShow.value = true
  res.data.time = timestempToDate(res.data.updatetime * 1000, '/')
  res.data.totalMoney = Math.floor(((res.data.vol * res.data.price * 1000) / 100000000) * 100) / 100
  gupiaoDetail.value = res.data
  collectInfo.value = res.data.is_zixuan
  buyGupiaoParams.value.price = res.data.price
}

const columns = [
  { label: t('gupiaoDetail.tip3'), value: 1 },
  { label: t('gupiaoDetail.tip4'), value: 2 },
  { label: t('gupiaoDetail.tip5'), value: 3 },
  { label: t('gupiaoDetail.tip6'), value: 4 },
  { label: t('gupiaoDetail.tip7'), value: 5 },
  { label: t('gupiaoDetail.tip8'), value: 6 },
  { label: t('gupiaoDetail.tip9'), value: 7 },
  { label: t('gupiaoDetail.tip10'), value: 8 }
]

const changeType = (e) => {
  buyGupiaoParams.value.buytype = e
  if (e === 1 && !price.value) {
    price.value = buyGupiaoParams.value.price
  }
}

// 股票K线
const dateActive = ref(1)

const chartValue = ref({})
const getChart = async (id: number, kline_type: number = 0) => {
  dateActive.value = kline_type

  const res = await getTickerKApi({ kline_type: dateActive.value, id })
  const kLineList = res.data.map((item: any) => {
    return {
      close: item.c,
      high: item.h,
      low: item.l,
      open: item.o,
      timestamp: item.t * 1000,
      volume: item.v
    }
  })
  chartValue.value = kLineList
}

// 收藏股票
const collectInfo = ref(0)
const collect = async () => {
  const res = await collectApi({ id: gupiaoDetail.value.id })
  const info = await collectDetailApi({ id: gupiaoDetail.value.id })
  collectInfo.value = info.data.msg === 'yes' ? 1 : 0
  showToast(res.data.msg)
}

// 购买股票
const buyGupiaoParams = ref<buyGupiaoType>({
  buytype: 0,
  fangxiang: 'duo',
  price: 0,
  pro_id: 0,
  shuliang: 1,
  ganggan_ratio: 10
})
const gupiaoWindowShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true,
  round: true
}
const heji = ref(0)
const openWindow = (e: string) => {
  const price1 = buyGupiaoParams.value.buytype === 0 ? Number(gupiaoDetail.value.price) : Number(price.value)
  const sxf = Number(parseFloat(userMoneyInfo.value.sxf * price1 * Number(buyGupiaoParams.value.shuliang)).toFixed(3))
  if (sxf > userMoneyInfo.value.sxfzdi) {
    shouxufei.value = sxf
  } else {
    shouxufei.value = Number(parseFloat(userMoneyInfo.value.sxfzdi).toFixed(3))
  }
  heji.value = (price1 || 0) * (buyGupiaoParams.value.shuliang || 0)
  gupiaoWindowShow.value = true
}

const priceChange = () => {
  if (price.value) {
    price.value = Number(price.value).toFixed(1)
  }
}

const buyGupiao = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  const params = { ...buyGupiaoParams.value }
  params.price = buyGupiaoParams.value.buytype === 0 ? Number(gupiaoDetail.value.price) : price.value !== '' ? Number(price.value) : Number(gupiaoDetail.value.price)
  params.ganggan_ratio = userMoneyInfo.value.peizi_data === '1' ? buyGupiaoParams.value.ganggan_ratio : 1
  const res = await buyGupiaoApi(params)
  if (res.code === 1) {
    closeToast()
    getUserMoney()
    showConfirmDialog({
      message: res.data.msg,
      confirmButtonText: t('gupiaoDetail.tip11'),
      cancelButtonText: t('gupiaoDetail.tip12')
    })
      .then(() => {
        // on confirm
        uni.switchTab({ url: '/pages/jiaoyi/jiaoyi' })
      })
      .catch(() => {
        // on cancel
      })
    setTimeout(() => {
      gupiaoWindowShow.value = false
    }, 1500)
  } else {
    closeToast()
    showToast({
      message: res.msg,
      // message: '您的餘額不足，請儲值\n2625000000.請儲值',
      wordBreak: 'break-all'
    })
  }
}

// 获取手续费
const userMoneyInfo = ref({})
const shouxufei = ref(0)
const getUserMoney = async () => {
  const res = await getUserMoneyApi()
  res.data.sxf = Number(res.data.sxf)
  res.data.sxfzdi = Number(res.data.sxfzdi)
  userMoneyInfo.value = res.data
}
</script>

<style scoped lang="scss">
.hh {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  word-break: break-all;
}
.collect {
  position: absolute;
  right: 0;
  height: 100%;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  image {
    width: 1.31rem;
    height: 1.31rem;
  }
}
.de {
  display: flex;
  align-items: center;
}
.text-left {
  text-align: left;
}
button {
  font-family: none;
}
.content {
  overflow: auto;
  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.7rem 0.94rem 0;
    .title {
      font-size: 0.94rem;
      color: $color-black;
      font-weight: 500;
    }
    .daima {
      font-size: 0.75rem;
      color: $color-gray;
    }
    .price {
      font-size: 1.5rem;
      font-weight: 500;
      color: $color-black;
    }
  }
  .box {
    width: 23.4375rem;
    height: 17.1875rem;
    border-top-left-radius: 1.5625rem;
    border-top-right-radius: 1.5625rem;
    background: #ffffff10;
    z-index: 9;
  }

  .zd {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-align: center;
    margin: 0 0.94rem;
  }

  .jiage {
    margin-top: 0.7188rem;
    text-align: center;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 500;
  }
  .card {
    margin: 0.5938rem auto 0;
    padding: 0 0.9375rem;
    border-radius: 1.03rem;
    margin-top: 0.34rem;
    text-align: left;

    .text-gray {
      color: #a6abb5;
      font-size: 0.9rem;
    }
    .text-value {
      font-size: 0.9rem;
      color: $color-black;
    }
    .de2 {
      display: flex;
      justify-content: flex-end;
      height: 1.13rem;
      margin: 0.2rem 0;
    }
  }
  .date_wrap {
    height: 2.13rem;
    margin: 0.9375rem 0.84rem 0;
    padding: 0.3125rem 0;
    border-radius: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.34rem;
    background: #ffffff10;
    border: 0.06rem solid #ffffff16;
    padding: 0 0.3rem;
    .date {
      text-align: center;
      position: relative;
      display: flex;
      align-items: center;
      span {
        width: 2.2rem;
        height: 1.5rem;
        border-radius: 0.63rem;
        line-height: 1.5rem;
        font-size: 0.75rem;
        color: $color-gray;
        transition: width 0.3s;
      }
    }
    .line {
      width: 0.06rem;
      height: 0.94rem;
      background: #aeaeae;
      margin: 0 0.2rem;
      border-radius: 0.19rem;
    }
    .active {
      span {
        width: 3rem;
        background-color: $color-primary;
        color: #fff;
      }
    }
  }
  .chart_wrap {
    padding: 0.56rem 1.25rem 2.56rem;
    margin-top: 0.34rem;
  }
  .button_wrap {
    padding: 1.25rem 1.25rem 0.625rem;
    display: flex;
    gap: 0.63rem;
    .button {
      background: $color-white;
      border: 0.1rem solid $color-primary;
      color: $color-black;
    }
    .active {
      background-color: $color-primary;
      color: #fff;
      border: 0.06rem solid $color-primary;
    }
  }
  .button {
    width: 10rem;
    height: 2.44rem;
    line-height: 2.44rem;
    text-align: center;
    color: #fff;
    font-size: 0.9375rem;
    background: #fff;
    border-radius: 0.5rem;
  }
  .submit {
    height: 3.0625rem !important;
    line-height: 3.0625rem !important;
    width: 100% !important;
    margin-top: 0.63rem !important;
    background-color: $color-primary;
    color: #fff;
  }
  .submit_wrap {
    border-radius: 1.03rem;
    padding: 0 1.56rem 1.25rem;
    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0.63rem 0;
      .title {
        font-size: 0.8125rem;
        color: $color-black;
      }
    }
    .wrap {
      .van-field {
        width: 15.0625rem;
        height: 2.0625rem;
        display: flex;
        align-items: center;
        border-radius: 1.25rem;
        border: 0.03rem solid #d2d2f8;
        padding: 0 0.88rem;
        margin-left: auto;
        margin-right: auto;
        font-size: 1rem;
        margin: 0.38rem 0 0.59rem;
      }
    }
    .button_wrap {
      padding: 0;
    }
  }
}

.buy_window {
  padding: 0 0.81rem 1.8rem 0.81rem;
  background: #fff;
  .row_group {
    display: flex;
    flex-wrap: wrap;
    .row {
      width: 50%;
    }
  }
  .title {
    text-align: center;
    padding: 0.8rem 0 0.5rem;
    font-size: 1rem;
    color: $color-black;
  }
  .row {
    display: flex;
    justify-content: space-between;
    padding: 0.64rem 0.56rem;
    div {
      font-size: 0.81rem;
    }
    .label {
      color: $color-gray;
    }
    .value {
      color: $color-black;
    }
  }
  .title {
    justify-content: center;
  }
  .button {
    height: 3.06rem;
    line-height: 3.06rem;
    width: 100%;
    text-align: center;
    border-radius: 0.5rem;
    background-color: $color-primary;
    font-size: 0.81rem;
    margin-top: 1.25rem;
    font-weight: 500;
    color: #fff;
  }
  .close {
    position: absolute;
    width: 2.75rem;
    height: 2.75rem;
    padding: 1rem;
    right: 0.5rem;
    top: 0rem;
  }
}

::v-deep .van-stepper__minus,
::v-deep .van-stepper__plus {
  width: 1.63rem;
  height: 1.63rem;
  border: 0.06rem solid $color-primary;
  border-radius: 50%;
  background: transparent;
  &::before {
    background: $color-primary;
  }
  &::after {
    background: $color-primary;
  }
}
::v-deep .van-stepper__minus--disabled,
::v-deep .van-stepper__plus--disabled {
  border-color: #ccc;
  &::before {
    background: #ccc;
  }
  &::after {
    background: #ccc;
  }
}

::v-deep .van-stepper__input {
  background: transparent;
  margin: 0 0.63rem;
  width: 5.6rem !important;
  color: $color-black;
}
</style>
