<template>
  <view class="tabbar">
    <view v-for="item in list" :key="item.id" class="bar" @click="switchTab(item)">
      <view class="card" :class="{ active: props.id == item.id }">
        <image class="image" :class="item.class" :src="props.id == item.id ? item.selectIcon : item.icon"></image>
        <view class="text">{{ item.text }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lange="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
// 导入图片
import indexIcon from '../../static/image/tabbar/index.png'
import indexSelectIcon from '../../static/image/tabbar/index_select.png'
import marketIcon from '../../static/image/tabbar/market.png'
import marketSelectIcon from '../../static/image/tabbar/market_select.png'
import jiaoyiIcon from '../../static/image/tabbar/jiaoyi.png'
import jiaoyiSelectIcon from '../../static/image/tabbar/jiaoyi_select.png'
import recordIcon from '../../static/image/tabbar/record.png'
import recordSelectIcon from '../../static/image/tabbar/record_select.png'
import userIcon from '../../static/image/tabbar/user.png'
import userSelectIcon from '../../static/image/tabbar/user_select.png'

const { t } = useI18n()
const props = defineProps({
  id: {
    type: Number,
    default: 0
  }
})
const text1 = computed(() => t('tabbar.index'))
const text2 = computed(() => t('tabbar.market'))
const text3 = computed(() => t('tabbar.jiaoyi'))
const text4 = computed(() => t('tabbar.record'))
const text5 = computed(() => t('tabbar.user'))
const list = ref([
  {
    path: '/pages/index/index',
    icon: indexIcon,
    selectIcon: indexSelectIcon,
    text: text1,
    id: 0,
    class: 'big'
  },
  {
    path: '/pages/market/market',
    icon: marketIcon,
    selectIcon: marketSelectIcon,
    text: text2,
    id: 1
  },
  {
    path: '/pages/jiaoyi/jiaoyi',
    icon: jiaoyiIcon,
    selectIcon: jiaoyiSelectIcon,
    text: text3,
    id: 2
  },
  {
    path: '/pages/record/record',
    icon: recordIcon,
    selectIcon: recordSelectIcon,
    text: text4,
    id: 3,
    class: 'big'
  },
  {
    path: '/pages/user/user',
    icon: userIcon,
    selectIcon: userSelectIcon,
    text: text5,
    id: 4
  }
])

const switchTab = (item) => {
  if (item.path) {
    uni.switchTab({
      url: item.path
    })
  }
}
</script>

<style scoped lang="scss">
.isIphoneX {
  padding-bottom: 2.13rem !important;
}
.tabbar {
  width: 100%;
  height: 4.375rem;
  display: flex;
  justify-content: space-around;
  position: fixed;
  z-index: 1000;
  bottom: 0;
  left: 0;
  box-shadow: 0 0.53rem 0.85rem 0.13rem #3d3d3d;
  background: #000e29;
  border-radius: 1rem 1rem 0 0;
  .bar {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card {
    width: 3.59rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 3.75rem;
    height: 4.375rem;
    .image {
      width: 1.5rem;
      height: 1.5rem;
      padding: 0.1rem;
      // transform: scale(2);
    }
    .big {
      transform: scale(1) !important;
    }
    .text {
      font-size: 0.69rem;
      color: $color-gray;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      word-break: break-all;
      position: relative;
      margin-top: 0.2rem;
    }
  }
  .active {
    width: 3.75rem;
    height: 4.375rem;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 2.38rem;
      height: 0.25rem;
      background: #fff;
      border-radius: 0 0 0.25rem 0.25rem;
      top: 0;
      left: 0;
      left: calc(50% - 1.19rem);
    }
    .text {
      color: $color-primary;
    }
  }
}
</style>
