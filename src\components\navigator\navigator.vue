<template>
  <view class="navigator">
    <slot name="left"></slot>
    <view v-if="porps.isShowBack" class="back" @click="goBack">
      <van-icon name="arrow-left" size="1.3rem" color="#111" />
    </view>
    <view class="title" style="display: flex; align-items: center; justify-content: center; width: 70%">
      <view class="title hh">{{ porps.title }}</view>
      <view v-if="porps.isdma !== ''" :style="{ width: '2.8125rem', color: '#111' }">{{ porps.isdma }}</view>
    </view>
    <slot name="right"></slot>
  </view>
</template>
<script lang="ts" setup>
const porps = defineProps({
  title: {
    type: String,
    default: ''
  },
  isShowBack: {
    type: Boolean,
    default: true
  },
  isdma: {
    type: String,
    default: ''
  }
})
const goBack = () => {
  const url = window.location.href
  if (url.indexOf('pages') >= 0) {
    uni.navigateBack({})
  } else {
    history.go(-1)
  }
}
</script>
<style lang="scss" scoped>
.navigator {
  height: 3.13rem;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.back {
  position: absolute;
  left: 0;
  height: 100%;
  padding-left: 1.0625rem;
  display: flex;
  align-items: center;
  image {
    width: 0.625rem;
    height: 1.0547rem;
  }
}
.title {
  font-size: 0.9375rem;
  color: $color-black;
  font-weight: 500;
  text-align: center;
}
</style>
